import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Installations, Users } from '../../database/entities';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils/logger';

interface PlatformUserInfo {
  platformUserId: string;
  lastLinkedAt: string;
  linkingStatus: 'pending' | 'success' | 'failed';
}

@Injectable()
export class UserPlatformLookupService {
  private readonly userCache = new Map<string, PlatformUserInfo | null>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly cacheTimestamps = new Map<string, number>();

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
  ) {}

  /**
   * Get platform user ID by Slack user ID and installation
   * @param slackUserId Slack user ID
   * @param installation Installation context
   * @returns Platform user info or null if not found
   */
  async getPlatformUserInfo(
    slackUserId: string,
    installation: Installations,
  ): Promise<PlatformUserInfo | null> {
    const cacheKey = `${installation.id}:${slackUserId}`;

    // Check cache first
    const cachedInfo = this.getCachedUserInfo(cacheKey);
    if (cachedInfo) {
      return cachedInfo;
    }

    try {
      // Fetch from database using QueryBuilder to avoid distinctAlias error
      const user = await this.usersRepository
        .createQueryBuilder('user')
        .select(['user.metadata'])
        .innerJoin('user.installation', 'installation')
        .where('user.slackId = :slackUserId', { slackUserId })
        .andWhere('installation.id = :installationId', { installationId: installation.id })
        .getOne();

      let platformUserInfo: PlatformUserInfo | null = null;

      if (user?.metadata?.platformUserId) {
        platformUserInfo = {
          platformUserId: user.metadata.platformUserId,
          lastLinkedAt: user.metadata.lastLinkedAt || '',
          linkingStatus: user.metadata.linkingStatus || 'success',
        };
      }

      // Cache the result
      this.setCachedUserInfo(cacheKey, platformUserInfo);

      return platformUserInfo;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to lookup platform user info for Slack user ${slackUserId}: ${errorMessage}`,
      );
      return null;
    }
  }

  /**
   * Get platform user ID by Slack user ID and installation (convenience method)
   * @param slackUserId Slack user ID
   * @param installation Installation context
   * @returns Platform user ID or null if not found
   */
  async getPlatformUserId(
    slackUserId: string,
    installation: Installations,
  ): Promise<string | null> {
    const userInfo = await this.getPlatformUserInfo(slackUserId, installation);
    return userInfo?.platformUserId || null;
  }

  /**
   * Check if a user has a valid platform user ID
   * @param slackUserId Slack user ID
   * @param installation Installation context
   * @returns True if user has a valid platform user ID
   */
  async hasValidPlatformUserId(
    slackUserId: string,
    installation: Installations,
  ): Promise<boolean> {
    const userInfo = await this.getPlatformUserInfo(slackUserId, installation);
    return userInfo?.linkingStatus === 'success' && !!userInfo.platformUserId;
  }

  /**
   * Refresh platform user mapping for a specific user
   * @param slackUserId Slack user ID
   * @param installation Installation context
   */
  async refreshUserMapping(
    slackUserId: string,
    installation: Installations,
  ): Promise<void> {
    const cacheKey = `${installation.id}:${slackUserId}`;

    // Remove from cache to force refresh
    this.userCache.delete(cacheKey);
    this.cacheTimestamps.delete(cacheKey);

    // Fetch fresh data
    await this.getPlatformUserInfo(slackUserId, installation);
  }

  /**
   * Clear all cached user mappings
   */
  clearCache(): void {
    this.userCache.clear();
    this.cacheTimestamps.clear();
    this.logger.log('Cleared user platform lookup cache');
  }

  /**
   * Get cached user info if available and not expired
   * @param cacheKey Cache key
   * @returns Cached user info or undefined if not found/expired
   */
  private getCachedUserInfo(cacheKey: string): PlatformUserInfo | null | undefined {
    const timestamp = this.cacheTimestamps.get(cacheKey);

    if (!timestamp || Date.now() - timestamp > this.CACHE_TTL) {
      // Cache expired, remove it
      this.userCache.delete(cacheKey);
      this.cacheTimestamps.delete(cacheKey);
      return undefined;
    }

    return this.userCache.get(cacheKey);
  }

  /**
   * Set cached user info
   * @param cacheKey Cache key
   * @param userInfo User info to cache
   */
  private setCachedUserInfo(cacheKey: string, userInfo: PlatformUserInfo | null): void {
    this.userCache.set(cacheKey, userInfo);
    this.cacheTimestamps.set(cacheKey, Date.now());
  }
}
